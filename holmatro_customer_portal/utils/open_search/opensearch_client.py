from typing import Any, Callable, Dict, List, Tuple

import sentry_sdk
from opensearchpy import OpenSearch
from opensearchpy.helpers import bulk

from holmatro_customer_portal.schemas.opensearch_schemas import (
    AutocompleteProduct,
    AutocompleteResponse,
    AutocompleteSuggestion,
    FilterConfig,
    OpenSearchSearchResponse,
    ProductDocument,
)
from holmatro_customer_portal.utils.env import Env
from holmatro_customer_portal.utils.logger import Logger
from holmatro_customer_portal.utils.open_search.opensearch_query_builder import OpenSearchQueryBuilder

_logger = Logger(__name__, Env.LOGGING_LEVEL.get())


class OpenSearchClient:
    def __init__(self, is_fuzzy_search_enabled: bool, index_name: str) -> None:
        self.client = OpenSearch(
            hosts=[{"host": Env.OPENSEARCH_HOST.get(), "port": Env.OPENSEARCH_PORT.get()}],
            http_auth=(
                (Env.OPENSEARCH_USERNAME.get(), Env.OPENSEARCH_PASSWORD.get())
                if Env.OPENSEARCH_USERNAME.get() and Env.OPENSEARCH_PASSWORD.get()
                else None
            ),
            use_ssl=Env.OPENSEARCH_USE_SSL.get(),
            ssl_show_warnings=True,
        )
        self.is_fuzzy_search_enabled = is_fuzzy_search_enabled
        self.index_name = index_name

    def create_index(self, mappings: Dict[str, Any]) -> None:
        try:
            if not self.client.indices.exists(index=self.index_name):
                self.client.indices.create(index=self.index_name, body={"mappings": mappings})
                _logger.info(f"Created index: {self.index_name}")
        except Exception as e:
            _logger.error(f"Error creating index {self.index_name}: {str(e)}")
            raise

    def delete_index(self) -> None:
        """Delete an index if it exists."""
        try:
            if self.client.indices.exists(index=self.index_name):
                self.client.indices.delete(index=self.index_name)
                _logger.info(f"Deleted index: {self.index_name}")
            else:
                _logger.info(f"Index {self.index_name} does not exist, nothing to delete")
        except Exception as e:
            _logger.error(f"Error deleting index {self.index_name}: {str(e)}")
            raise

    def reindex(self, mappings: Dict[str, Any], documents: list[ProductDocument]) -> Tuple[int, list]:
        """Delete existing index, create new one with mappings, and bulk index documents."""
        try:
            # Delete existing index
            self.delete_index()

            # Create new index with mappings
            self.create_index(mappings)

            # Bulk index all documents
            return self.bulk_index(documents)

        except Exception as e:
            _logger.error(f"Error during reindexing {self.index_name}: {str(e)}")
            raise

    def bulk_index(self, documents: list[ProductDocument]) -> Tuple[int, list]:
        try:
            actions = [
                {
                    "_index": self.index_name,
                    "_id": doc.id,
                    "_source": doc.model_dump(),
                }
                for doc in documents
            ]
            success, failed = bulk(self.client, actions)
            _logger.info(f"Bulk indexed {success} documents to {self.index_name}")
            if failed:
                _logger.error(f"Failed to bulk index {len(failed)} documents: {failed}")
            return success, failed
        except Exception as e:
            _logger.error(f"Error during bulk indexing: {str(e)}")
            raise

    def update_documents(self, documents: list[ProductDocument]) -> Tuple[int, list]:
        """Update or insert documents in the index (upsert operation)."""
        try:
            actions = [
                {
                    "_index": self.index_name,
                    "_id": doc.id,
                    "_op_type": "update",
                    "doc": doc.model_dump(),
                    "doc_as_upsert": True,
                }
                for doc in documents
            ]
            success, failed = bulk(self.client, actions)
            _logger.info(f"Updated/inserted {success} documents in {self.index_name}")
            if failed:
                _logger.error(f"Failed to update {len(failed)} documents: {failed}")
            return success, failed
        except Exception as e:
            _logger.error(f"Error during document update: {str(e)}")
            raise

    def delete_documents(self, document_ids: list[str]) -> Tuple[int, list]:
        """Delete documents by their IDs from the index."""
        try:
            actions = [{"_index": self.index_name, "_id": doc_id, "_op_type": "delete"} for doc_id in document_ids]
            success, failed = bulk(self.client, actions, ignore_status=[404])  # Ignore if doc doesn't exist
            _logger.info(f"Deleted {success} documents from {self.index_name}")
            if failed:
                _logger.error(f"Failed to delete {len(failed)} documents: {failed}")
            return success, failed
        except Exception as e:
            _logger.error(f"Error during document deletion: {str(e)}")
            raise

    def delta_update(self, upsert_documents: list[ProductDocument], delete_ids: list[str]) -> dict:
        """Perform delta updates: upsert some documents and delete others."""
        results: dict = {"upserted": (0, []), "deleted": (0, [])}

        try:
            if upsert_documents:
                results["upserted"] = self.update_documents(upsert_documents)

            if delete_ids:
                results["deleted"] = self.delete_documents(delete_ids)

            _logger.info(
                f"Delta update completed - Upserted: {results['upserted'][0]}, Deleted: {results['deleted'][0]}"
            )
            return results

        except Exception as e:
            _logger.error(f"Error during delta update: {str(e)}")
            raise

    def search_products(
        self,
        search_query: str | None = None,
        category_id: int | None = None,
        page_number: int = 1,
        page_size: int = 100,
    ) -> OpenSearchSearchResponse:
        """
        Search for products in the OpenSearch index.

        Args:
            search_query: General search query (searches across all relevant fields from opensearch_mappings.py)
            category_id: Filter results to specific category ID (e.g., 2838)
            page_number: Page number for pagination (1-based)
            page_size: Number of results per page

        Returns:
            OpenSearchSearchResponse: OpenSearch response including hits and aggregations
        """
        try:
            query_builder = OpenSearchQueryBuilder(self.is_fuzzy_search_enabled)
            # Only pass search_query and category_id to build the query for search_products
            body = query_builder.build(
                search_query=search_query,
                category_id=category_id,
                filters=None,
                page_number=page_number,
                page_size=page_size,
            )

            response = self.client.search(index=self.index_name, body=body)

            hits = response.get("hits", {"total": {"value": 0}, "hits": []})

            return OpenSearchSearchResponse(hits=hits, aggregations=None)

        except Exception as e:
            _logger.error(f"Error during product search: {str(e)}")
            sentry_sdk.capture_exception(e)
            # Return empty response structure on error
            return OpenSearchSearchResponse(hits={"total": {"value": 0}, "hits": []}, aggregations=None)

    def filter_products(
        self,
        category_id: int | None = None,
        filters: List[FilterConfig] | None = None,
        page_number: int = 1,
        page_size: int = 100,
    ) -> OpenSearchSearchResponse:
        """
        Filter products in the OpenSearch index based on dynamic filters and category ID.

        Args:
            category_id: Filter results to specific category ID (e.g., 2838)
            filters: List of FilterConfig objects for dynamic filtering
            page_number: Page number for pagination (1-based)
            page_size: Number of results per page

        Returns:
            OpenSearchSearchResponse: OpenSearch response including hits and aggregations
        """
        try:
            query_builder = OpenSearchQueryBuilder(self.is_fuzzy_search_enabled)
            body = query_builder.build(
                category_id=category_id,
                filters=filters,
                page_number=page_number,
                page_size=page_size,
                build_aggregations=True,
            )

            response = self.client.search(index=self.index_name, body=body)

            hits = response.get("hits", {"total": {"value": 0}, "hits": []})
            aggregations = response.get("aggregations")

            return OpenSearchSearchResponse(hits=hits, aggregations=aggregations)

        except Exception as e:
            _logger.error(f"Error during product filtering: {str(e)}")
            sentry_sdk.capture_exception(e)
            # Return empty response structure on error
            return OpenSearchSearchResponse(hits={"total": {"value": 0}, "hits": []}, aggregations=None)

    @staticmethod
    def _deduplicate_and_sort(items: list, key_func: Callable, max_items: int) -> list:
        """
        Generic helper to deduplicate items and sort by score.
        """
        unique_items: dict = {}
        for item in items:
            key = key_func(item)
            item_score = getattr(item, "score")
            if key not in unique_items or getattr(unique_items[key], "score") < item_score:
                unique_items[key] = item

        # Sort by score (relevance) and limit results
        return sorted(unique_items.values(), key=lambda x: getattr(x, "score"), reverse=True)[:max_items]

    @staticmethod
    def _process_suggestion_options(
        suggest_results: list,
    ) -> tuple[list[AutocompleteSuggestion], list[AutocompleteProduct]]:
        """
        Helper method to process suggestion options and extract suggestions and products.
        """
        suggestions = []
        products = []

        for suggest_result in suggest_results:
            for option in suggest_result["options"]:
                suggestions.append(AutocompleteSuggestion(text=option["text"], score=option["_score"]))
                products.append(AutocompleteProduct(**option["_source"], score=option["_score"]))

        return suggestions, products

    def autocomplete(
        self,
        search_query: str,
        max_suggestions: int = 10,
    ) -> AutocompleteResponse:
        """
        Provide autocomplete suggestions using OpenSearch Completion Suggester.
        """
        try:
            body: Dict[str, Any] = {
                "_source": ["id", "product_id", "product_names"],  # Only fetch required fields
                "suggest": {
                    "product_name_suggest": {
                        "prefix": search_query,
                        "completion": {
                            "field": "product_name_suggest",
                            "size": max_suggestions,
                            "skip_duplicates": True,
                        },
                    },
                    "article_number_suggest": {
                        "prefix": search_query,
                        "completion": {
                            "field": "article_number_suggest",
                            "size": max_suggestions,
                            "skip_duplicates": True,
                        },
                    },
                },
            }

            # Add fuzzy matching for typo tolerance
            if self.is_fuzzy_search_enabled and len(search_query) > 2:  # Only use fuzzy for longer queries
                fuzzy_config = {
                    "fuzziness": 1,
                    "transpositions": True,
                    "min_length": 3,
                    "prefix_length": 1,
                }
                body["suggest"]["product_name_suggest"]["completion"]["fuzzy"] = fuzzy_config

            response = self.client.search(index=self.index_name, body=body)

            # Extract suggestions and products using helper method
            all_suggestions = []
            all_products = []

            if "suggest" in response:
                # Process different suggestion types
                suggestion_configs = ["product_name_suggest", "article_number_suggest"]

                for suggest_key in suggestion_configs:
                    if suggest_key in response["suggest"]:
                        suggestions, products = self._process_suggestion_options(response["suggest"][suggest_key])
                        all_suggestions.extend(suggestions)
                        all_products.extend(products)

            # Deduplicate and sort suggestions by text
            sorted_suggestions = self._deduplicate_and_sort(
                all_suggestions, key_func=lambda x: x.text, max_items=max_suggestions
            )

            # Deduplicate and sort products by product ID
            sorted_products = self._deduplicate_and_sort(
                all_products, key_func=lambda x: str(x.id), max_items=max_suggestions
            )

            return AutocompleteResponse(
                suggestions=[s.text for s in sorted_suggestions],
                products=sorted_products,
            )

        except Exception as e:
            _logger.error(f"Error during autocomplete search: {str(e)}")
            # Return empty response on error
            return AutocompleteResponse(
                suggestions=[],
                products=[],
            )

    def get_attribute_aggregation(self, attribute_name: str, category_id: int | None = None) -> Any:
        try:
            # Build the complete query using the static method
            body = OpenSearchQueryBuilder.build_attribute_aggregation(attribute_name, category_id)

            response = self.client.search(index=self.index_name, body=body)

            # Return the aggregations part of the response
            return response.get("aggregations")

        except Exception as e:
            _logger.error(
                f"Error getting attribute aggregation for '{attribute_name}' in category {category_id}: {str(e)}"
            )
            sentry_sdk.capture_exception(e)
            return None
