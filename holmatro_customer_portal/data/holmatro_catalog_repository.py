from fastapi import HTTPException
from sqlalchemy.orm import joinedload

from holmatro_customer_portal.data.mappers.auto_complete_mapper import AutoCompleteMapper
from holmatro_customer_portal.data.mappers.category_mapper import CategoryMapper
from holmatro_customer_portal.database.enums import DBFilterType, LanguageEnum
from holmatro_customer_portal.database.models import Category as DBCategory
from holmatro_customer_portal.database.models import CategoryTranslation, Language, User
from holmatro_customer_portal.domain.repositories.catalog_repository import CatalogRepository
from holmatro_customer_portal.mappers.opensearch_request_mapper import OpenSearchRequestMapper
from holmatro_customer_portal.mappers.opensearch_response_mapper import OpenSearchResponseMapper
from holmatro_customer_portal.schemas.response_schema import ParamsReq, SearchFiltersReq, SearchParamsReq
from holmatro_customer_portal.services.configurator.configurator_enums import FilterName
from holmatro_customer_portal.utils.database import Session
from holmatro_customer_portal.utils.enums import SyncforceCategory, get_language_code_from_id
from holmatro_customer_portal.utils.open_search.opensearch_client import OpenSearchClient
from holmatro_customer_portal.utils.tweakwise.category_path_utils import find_category_path_in_tree
from holmatro_customer_portal.utils.tweakwise.tweakwise_category_path import get_tweakwise_category
from holmatro_customer_portal.utils.tweakwise.tweakwise_schema import (
    Autocomplete,
    Category,
    FacetsAttributes,
    FilterAttribute,
    Navigation,
    ProductsResponse,
)


class HolmatroCatalogRepository(CatalogRepository):

    def __init__(self, db: Session, opensearch_client: OpenSearchClient):
        self.db = db
        self.opensearch_client = opensearch_client
        self.response_mapper = OpenSearchResponseMapper(db)

    def get_facet_attributes(
        self, urlkey: str, category: SyncforceCategory, parent_category: SyncforceCategory
    ) -> list[FacetsAttributes | FilterAttribute] | None:

        filter_type = DBFilterType.SLIDER
        if urlkey == FilterName.TONNAGE.value:
            attribute_id = 99
            filter_type = DBFilterType.CHECKBOX
        elif urlkey == FilterName.CYLINDER_TYPE.value:
            attribute_id = 363
        elif urlkey == FilterName.CAPACITY_OIL_TANK.value:
            attribute_id = 384

        # Get raw aggregation results from OpenSearchClient
        aggregation_results = self.opensearch_client.get_attribute_aggregation(attribute_id, category.value)

        # Use OpenSearchResponseMapper to map results to FilterAttribute objects (English only, no translations)
        return self.response_mapper.map_attribute_aggregation_to_filter_attributes(
            attribute_id, aggregation_results, filter_type, LanguageEnum.ENGLISH.value
        )  # type: ignore[return-value]  # FilterAttribute is compatible with FacetsAttributes | FilterAttribute

    def get_category_path(
        self, language: str, category: SyncforceCategory, parent_category: SyncforceCategory
    ) -> str | None:
        parent_category_path = get_tweakwise_category(language, parent_category)

        tree = self.get_category_tree(parent_category_path)

        return find_category_path_in_tree(tree, language, category)

    def get_autocomplete(self, params: SearchParamsReq) -> Autocomplete:
        query = params.search_query

        # Get autocomplete response from OpenSearch
        opensearch_response = self.opensearch_client.autocomplete(query)

        # Use the mapper to convert AutocompleteResponse to Autocomplete schema
        mapper = AutoCompleteMapper()
        return mapper.convert_opensearch_response_to_autocomplete(opensearch_response)

    def get_navigation(self, user: User, params: ParamsReq, filters: SearchFiltersReq | None = None) -> Navigation:
        """
        Retrieve navigation data including products and filter facets based on search parameters.
        """
        product_category_id = None
        filter_category_id = None
        if params.category_path:
            parts = params.category_path.split("-")
            product_category_id = int(parts[-1][-4:])
            filter_category_id = int(parts[0][-4:])

        filter_configs = OpenSearchRequestMapper.map_filters_to_opensearch(filters) if filters else None

        response = self.opensearch_client.filter_products(
            category_id=product_category_id,
            filters=filter_configs,
            page_number=params.page_number,
            page_size=200,
        )

        # Convert OpenSearch results to Navigation using mapper
        return self.response_mapper.map_to_navigation_response(
            response, params.page_number, params.products_per_page, user, filter_category_id, filters
        )

    def get_category_tree(self, category_id: int) -> Category:
        # Split category_id into language_id (first 4 digits) and remaining category_id
        category_id_str = str(category_id)
        language_id = category_id_str[:4]
        remaining_category_id = category_id_str[4:]

        root_category = self._load_category_with_children(remaining_category_id, language_id)

        if not root_category:
            raise HTTPException(status_code=404, detail=f"Category with ID {remaining_category_id} not found")

        # Convert DBCategory to Category schema
        category = CategoryMapper().convert_db_category_to_schema(root_category, language_id)

        return category

    def _load_category_with_children(self, category_id: str, language_id: str) -> DBCategory | None:
        """
        Load a category and all its children recursively using eager loading.
        Only loads translations for the specified language.

        Args:
            category_id: The category ID to load
            language_id: The numeric language ID (e.g., 2000 for NL)

        Returns:
            DBCategory with all children loaded and language-specific translations, or None if not found
        """
        language_code = get_language_code_from_id(int(language_id))
        if not language_code:
            language_uuid = None
        else:
            # Find the Language record by language_code
            language = self.db.query(Language).filter_by(language_code=language_code.lower()).first()
            language_uuid = language.id if language else None

        # Build the joinedload options for nested children
        options = [
            joinedload(DBCategory.children),
            joinedload(DBCategory.translations).joinedload(CategoryTranslation.language),
        ]

        category = self.db.query(DBCategory).options(*options).filter_by(category_id=category_id).first()

        if category:
            self._process_category_tree(category, language_uuid)

        return category

    def _process_category_tree(self, category: DBCategory, language_uuid: str | None, max_depth: int = 10) -> None:
        """
        Recursively process category tree in a single traversal:
        1. Filter translations to only include the specified language (if provided)
        2. Sort children by their sort_index while maintaining tree structure

        This is more efficient than separate traversals for filtering and sorting.

        Args:
            category: The category to process
            language_uuid: The language UUID to keep (None to keep all translations)
            max_depth: Maximum depth to prevent infinite recursion
        """
        if max_depth <= 0 or not category:
            return

        # Filter translations for current category (if language_uuid provided)
        if language_uuid:
            category.translations = [
                translation for translation in category.translations if translation.language_id == language_uuid
            ]

        # Sort children by sort_index (ascending order)
        if hasattr(category, "children") and category.children:
            category.children = sorted(category.children, key=lambda c: c.sort_index)

            # Recursively process children (filter translations + sort grandchildren)
            for child in category.children:
                self._process_category_tree(child, language_uuid, max_depth - 1)

    def get_products(self, params: ParamsReq, filters: SearchFiltersReq | None = None) -> ProductsResponse:
        """
        Search for products using OpenSearch based on the provided parameters.
        """
        category_id = self._get_category_id_from_path(params)

        response = self.opensearch_client.search_products(
            search_query=params.search_query,
            category_id=category_id,
        )

        # Convert OpenSearch results to ProductsResponse using mapper
        return self.response_mapper.map_to_products_response(response, params.page_number, params.products_per_page)

    @staticmethod
    def _get_category_id_from_path(params: ParamsReq) -> int | None:
        # Extract category_id from category_path if provided
        category_id = None
        if params.category_path:
            # Split by hyphen and take the last part, then extract the last 4 digits
            parts = params.category_path.split("-")
            category_id = int(parts[-1][-4:])
        return category_id
